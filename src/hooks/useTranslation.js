"use client";

import { useParams } from 'next/navigation';
import { useMemo } from 'react';

// Import all translation files
import frCommon from '../../locales/fr/common.json';
import enCommon from '../../locales/en/common.json';
import frNavigation from '../../locales/fr/navigation.json';
import enNavigation from '../../locales/en/navigation.json';
import frFooter from '../../locales/fr/footer.json';
import enFooter from '../../locales/en/footer.json';
import frLegal from '../../locales/fr/legal.json';
import enLegal from '../../locales/en/legal.json';
import frProjects from '../../locales/fr/projects.json';
import enProjects from '../../locales/en/projects.json';

// Import page-specific translations
import frHome from '../../locales/fr/home.json';
import enHome from '../../locales/en/home.json';
import frContact from '../../locales/fr/contact.json';
import enContact from '../../locales/en/contact.json';
import frAgency from '../../locales/fr/agency.json';
import enAgency from '../../locales/en/agency.json';
import frBlog from '../../locales/fr/blog.json';
import enBlog from '../../locales/en/blog.json';
import frFaq from '../../locales/fr/faq.json';
import enFaq from '../../locales/en/faq.json';
import frNotFound from '../../locales/fr/not-found.json';
import enNotFound from '../../locales/en/not-found.json';

const translations = {
  fr: {
    common: frCommon,
    navigation: frNavigation,
    footer: frFooter,
    legal: frLegal,
    projects: frProjects,
    // Page-specific translations
    home: frHome,
    contact: frContact,
    agency: frAgency,
    blog: frBlog,
    faq: frFaq,
    'not-found': frNotFound,
  },
  en: {
    common: enCommon,
    navigation: enNavigation,
    footer: enFooter,
    legal: enLegal,
    projects: enProjects,
    // Page-specific translations
    home: enHome,
    contact: enContact,
    agency: enAgency,
    blog: enBlog,
    faq: enFaq,
    'not-found': enNotFound,
  },
};

export function useTranslation(namespace = 'common') {
  const params = useParams();
  const locale = params?.locale || 'fr';

  const t = useMemo(() => {
    return (key, variables = {}) => {
      const keys = key.split('.');
      let value = translations[locale][namespace];

      for (const k of keys) {
        if (value && typeof value === 'object') {
          value = value[k];
        } else {
          break;
        }
      }

      if (typeof value !== 'string' && !Array.isArray(value)) {
        console.warn(`Translation key "${key}" not found in namespace "${namespace}" for locale "${locale}"`);
        return key;
      }

      // If it's an array, return it as is (for components to handle)
      if (Array.isArray(value)) {
        return value;
      }

      // Replace variables in the translation (only for strings)
      return value.replace(/\{(\w+)\}/g, (match, variable) => {
        return variables[variable] || match;
      });
    };
  }, [locale, namespace]);

  return { t, locale };
}

// Helper function to get translation without hook (for use in getStaticProps, etc.)
export function getTranslation(locale = 'fr', namespace = 'common') {
  return (key, variables = {}) => {
    const keys = key.split('.');
    let value = translations[locale][namespace];

    for (const k of keys) {
      if (value && typeof value === 'object') {
        value = value[k];
      } else {
        break;
      }
    }

    if (typeof value !== 'string' && !Array.isArray(value)) {
      console.warn(`Translation key "${key}" not found in namespace "${namespace}" for locale "${locale}"`);
      return key;
    }

    // If it's an array, return it as is (for components to handle)
    if (Array.isArray(value)) {
      return value;
    }

    // Replace variables in the translation (only for strings)
    return value.replace(/\{(\w+)\}/g, (match, variable) => {
      return variables[variable] || match;
    });
  };
}
