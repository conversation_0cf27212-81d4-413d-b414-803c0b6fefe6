.project {
    width: 100%;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    color: inherit;
    display: block;
  
    &:hover,
    &:active,
    &:focus {
      text-decoration: none;
      outline: none;
      color: inherit;
    }
  
    .gridLayout {
      display: grid; /* Les éléments sont empilés */
      padding: 20px 0;
      grid-template-columns: 1fr;
      
      .imageWrapper {
        width: 100%;
      }
  
      .projectTitle {
        margin: 0;
        padding-top: 25px;
        font-size: 24px;
              letter-spacing: -1px;
      word-spacing: 3px;
        font-weight: 400;
      }
  
      .separator {
        width: 100%;
        height: 1px;
        background-color: rgba(28, 29, 32, 0.176);
        margin-top: calc(var(--gap-padding) / 2);
        margin-bottom: calc(var(--gap-padding) / 2);
      }
  
      .metaRow {
        display: flex;
        justify-content: space-between;
  
        .services,
        .year {
          font-weight: 300;
          font-size: 14px;
          text-align: center;
        }
      }
    }
  }
  
  .nextCaseRow {
display: flex;
flex-direction: column;
gap: var(--gap-padding)
  }


  @media screen and (min-width:768px) {
    .nextCaseRow {
        flex-direction: row;
    }
  }

  .nextCaseHeading {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--gap-padding);
  }

  .nextCase {
    padding-top: var(--section-padding);
    padding-bottom: var(--section-padding);
  }