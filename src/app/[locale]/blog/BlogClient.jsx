"use client";

import { allPosts } from '@/lib/contentlayer-replacement';
import Link from 'next/link';
import Image from 'next/image';
import Hero from '@/components/Heros/Hero';
import styles from "./page.module.scss";
import Mask from '@/components/Mask';
import { useTranslation } from "@/hooks/useTranslation";

export default function BlogClient({ params }) {
  const { t } = useTranslation('blog');
  const locale = params.locale || 'fr';

  // Récupérer et trier les posts publiés pour la langue courante
  const posts = allPosts
    .filter((p) => p.published && p.locale === locale)
    .sort((a, b) => +new Date(b.date) - +new Date(a.date));

  return (
    <main>
      <Hero
        title={t('hero_title')}
        subtitle={t('hero_subtitle')}
        locale={locale}
      />

      {/* Reproduction exacte du design original */}
      <section className={`container ${styles.grid}`}>
        {posts.map((post, i) => {
          const slug = post.slug;
          const row = Math.floor(i/2);
          const pos = i%2;
          const odd = row % 2 === 1;

          // sur les lignes impaires, on inverse l'ordre (logique originale)
          const variant = odd
            ? pos===0 ? styles.big   : styles.small
            : pos===0 ? styles.small : styles.big;

          return (
            <article key={post._id} className={`${styles.card} ${variant}`}>
              <Link href={`/${locale}/blog/${slug}`} className={styles.blogLink}>
                <div className={styles.imageWrapper}>
                  <Mask wrap hoverAnim color="transparent" disableMobile>
                    <Image
                      src={post.cover||'/images/placeholder.jpg'}
                      alt={post.title}
                      fill
                      className={styles.image}
                      sizes={
                        variant===styles.big
                        ? '(min-width:1024px) 60vw, 100vw'
                        : '(min-width:1024px) 40vw, 100vw'
                      }
                    />
                  </Mask>
                </div>
                <p className={`${styles.title} text-big`}>{post.title}</p>
                {post.category && (
                  <p className={styles.category}>{post.category}</p>
                )}
              </Link>
            </article>
          );
        })}
      </section>
    </main>
  );
}
