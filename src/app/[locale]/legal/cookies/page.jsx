import { allLegals } from '@/lib/contentlayer-replacement';
import { notFound } from 'next/navigation';
import CookiesClient from './CookiesClientComponent';

export function generateMetadata({ params }) {
  const locale = params.locale || 'fr';
  const legal = allLegals.find((l) => l.slug === 'cookies' && l.locale === locale);

  if (!legal) notFound();

  return {
    title: legal.title,
    description: legal.description,
    openGraph: {
      title: legal.title,
      description: legal.description,
      images: [{ url: "https://kapreon.com/og-default.jpg" }],
      url: `https://kapreon.com/${locale}/legal/cookies`,
      locale: locale === 'fr' ? 'fr_CA' : 'en_CA',
    },
    twitter: {
      card: "summary_large_image",
      title: legal.title,
      description: legal.description,
      images: ["https://kapreon.com/og-default.jpg"],
    },
  };
}

export default function Cookies({ params }) {
  return <CookiesClient params={params} />;
}
