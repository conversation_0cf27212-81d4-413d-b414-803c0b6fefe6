'use client';

import { useTranslation } from '@/hooks/useTranslation';
import Link from 'next/link';
import styles from './page.module.scss';

export default function TransitionDemoClient({ params }) {
  const locale = params.locale || 'fr';
  const { t } = useTranslation('navigation');

  return (
    <main className={styles.main}>
      {/* Navigation simple */}
      <nav className={styles.nav}>
        <Link href={`/${locale}`}>Accueil</Link>
        <Link href={`/${locale}/agency`}>Agence</Link>
        <Link href={`/${locale}/blog`}>Blog</Link>
        <Link href={`/${locale}/contact`}>Contact</Link>
      </nav>

      {/* Conteneur principal */}
      <div className={styles.container}>
        {/* Titre héro */}
        <div className={styles.hero}>
          <h1>Transitions</h1>
        </div>

        {/* Section avec images */}
        <div className={styles.images}>
          <img src="/images/lucas-joliveau-siege-ordinateur-portable.png" alt="Demo 1" />
          <img src="/images/lucas-joliveau-siege-ordinateur-portable.png" alt="Demo 2" />
          <img src="/images/lucas-joliveau-siege-ordinateur-portable.png" alt="Demo 3" />
        </div>

        {/* Section info avec texte animé */}
        <div className={styles.info}>
          <div className={styles.col}>
            <img src="/images/lucas-joliveau-siege-ordinateur-portable.png" alt="Portrait" />
          </div>
          <div className={styles.col}>
            <p>
              Cette page démontre les transitions modernes utilisant l&apos;API View Transitions du navigateur.
              Les animations sont fluides et performantes, créant une expérience utilisateur immersive.
              Naviguez entre les pages pour voir l&apos;effet en action !
            </p>
          </div>
        </div>

        {/* Liens de test */}
        <div className={styles.testLinks}>
          <h2>Tester les transitions :</h2>
          <div className={styles.linkGrid}>
            <Link href={`/${locale}`}>← Retour à l&apos;accueil</Link>
            <Link href={`/${locale}/agency`}>Voir l&apos;agence →</Link>
            <Link href={`/${locale}/blog`}>Lire le blog →</Link>
            <Link href={`/${locale}/contact`}>Nous contacter →</Link>
          </div>
        </div>
      </div>
    </main>
  );
}
