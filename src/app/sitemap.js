import { allPosts } from '@/lib/contentlayer-replacement';
import allProjects from '../data/projectsData';

export default function sitemap() {
  const baseUrl = 'https://kapreon.com';
  const locales = ['fr', 'en'];
  
  // Static pages
  const staticPages = [
    '',
    '/contact',
    '/questions-frequentes',
    '/blog',
    '/legal/cookies',
    '/legal/termes-conditions',
  ];

  // Generate URLs for static pages in both languages
  const staticUrls = locales.flatMap(locale => 
    staticPages.map(page => ({
      url: `${baseUrl}${locale === 'fr' ? '' : '/en'}${page}`,
      lastModified: new Date(),
      changeFrequency: page === '' ? 'weekly' : 'monthly',
      priority: page === '' ? 1 : 0.8,
    }))
  );

  // Generate URLs for blog posts (now organized by language)
  const blogUrls = allPosts
    .filter(post => post.published)
    .map(post => ({
      url: `${baseUrl}${post.locale === 'fr' ? '' : '/en'}/blog/${post.slug}`,
      lastModified: new Date(post.updated || post.date),
      changeFrequency: 'monthly',
      priority: 0.7,
    }));

  // Generate URLs for projects in both languages
  const projectUrls = locales.flatMap(locale =>
    allProjects.map(project => ({
      url: `${baseUrl}${locale === 'fr' ? '' : '/en'}/projets/${project.slug}`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.8,
    }))
  );

  return [
    ...staticUrls,
    ...blogUrls,
    ...projectUrls,
  ];
}
